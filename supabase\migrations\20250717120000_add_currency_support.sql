<<<<<<<
-- Add currency support to expenses table



-- Migration: 20250717120000_add_currency_support.sql



-- Description: Add multi-currency support with KWD as default currency







-- Add currency fields to expenses table



ALTER TABLE public.expenses 



ADD COLUMN currency_code TEXT NOT NULL DEFAULT 'KWD',



ADD COLUMN original_amount DECIMAL(10,2),



ADD COLUMN exchange_rate DECIMAL(10,6) NOT NULL DEFAULT 1.0;







-- Add check constraint for valid currency codes (ISO 4217 format)



ALTER TABLE public.expenses 



ADD CONSTRAINT check_currency_code_format 



CHECK (currency_code ~ '^[A-Z]{3}$');







-- Add check constraint for positive exchange rate



ALTER TABLE public.expenses 



ADD CONSTRAINT check_positive_exchange_rate 



CHECK (exchange_rate > 0);







-- Add check constraint for positive original amount when provided



ALTER TABLE public.expenses 



ADD CONSTRAINT check_positive_original_amount 



CHECK (original_amount IS NULL OR original_amount > 0);







-- Create index for currency-based queries



CREATE INDEX idx_expenses_currency_code ON public.expenses(currency_code);



CREATE INDEX idx_expenses_user_currency ON public.expenses(user_id, currency_code);







-- Update existing records to set original_amount equal to amount for KWD (default currency)



-- This ensures backward compatibility with existing data



UPDATE public.expenses 



SET original_amount = amount 



WHERE original_amount IS NULL;







-- Make original_amount NOT NULL after setting values for existing records



ALTER TABLE public.expenses 



ALTER COLUMN original_amount SET NOT NULL;







-- Add comment to document the currency fields



COMMENT ON COLUMN public.expenses.currency_code IS 'ISO 4217 currency code (e.g., KWD, USD, EUR)';



COMMENT ON COLUMN public.expenses.original_amount IS 'Original amount in the specified currency before conversion';



COMMENT ON COLUMN public.expenses.exchange_rate IS 'Exchange rate used to convert to base currency (KWD), default 1.0 for KWD';







-- Create function to validate currency operations



CREATE OR REPLACE FUNCTION public.validate_currency_data()



RETURNS TRIGGER AS $$



BEGIN



  -- If currency is KWD, ensure exchange rate is 1.0 and original_amount equals amount



  IF NEW.currency_code = 'KWD' THEN



    NEW.exchange_rate := 1.0;



    NEW.original_amount := NEW.amount;



  END IF;



  



  -- If currency is not KWD, ensure original_amount and exchange_rate are properly set



  IF NEW.currency_code != 'KWD' THEN



    -- Calculate amount in KWD if not already set correctly



    IF NEW.original_amount IS NOT NULL AND NEW.exchange_rate IS NOT NULL THEN



      NEW.amount := NEW.original_amount * NEW.exchange_rate;



    END IF;



  END IF;



  



  RETURN NEW;



END;



$$ LANGUAGE plpgsql;







-- Create trigger to validate currency data on insert/update



CREATE TRIGGER validate_currency_data_trigger



  BEFORE INSERT OR UPDATE ON public.expenses



  FOR EACH ROW



  EXECUTE FUNCTION public.validate_currency_data();



=======
-- Add currency support to expenses table

-- Migration: 20250717120000_add_currency_support.sql

-- Description: Add multi-currency support with KWD as default currency



-- Add currency fields to expenses table

ALTER TABLE public.expenses 

ADD COLUMN currency_code TEXT NOT NULL DEFAULT 'KWD',

ADD COLUMN original_amount DECIMAL(10,2),

ADD COLUMN exchange_rate DECIMAL(10,6) NOT NULL DEFAULT 1.0;



-- Add check constraint for valid currency codes (ISO 4217 format)

ALTER TABLE public.expenses 

ADD CONSTRAINT check_currency_code_format 

CHECK (currency_code ~ '^[A-Z]{3}$');



-- Add check constraint for positive exchange rate

ALTER TABLE public.expenses 

ADD CONSTRAINT check_positive_exchange_rate 

CHECK (exchange_rate > 0);



-- Add check constraint for positive original amount when provided

ALTER TABLE public.expenses 

ADD CONSTRAINT check_positive_original_amount 

CHECK (original_amount IS NULL OR original_amount > 0);



-- Create index for currency-based queries

CREATE INDEX idx_expenses_currency_code ON public.expenses(currency_code);

CREATE INDEX idx_expenses_user_currency ON public.expenses(user_id, currency_code);



-- Update existing records to set original_amount equal to amount for KWD (default currency)

-- This ensures backward compatibility with existing data

UPDATE public.expenses 

SET original_amount = amount 

WHERE original_amount IS NULL;



-- Make original_amount NOT NULL after setting values for existing records

ALTER TABLE public.expenses 

ALTER COLUMN original_amount SET NOT NULL;



-- Add comment to document the currency fields

COMMENT ON COLUMN public.expenses.currency_code IS 'ISO 4217 currency code (e.g., KWD, USD, EUR)';

COMMENT ON COLUMN public.expenses.original_amount IS 'Original amount in the specified currency before conversion';

COMMENT ON COLUMN public.expenses.exchange_rate IS 'Exchange rate used to convert to base currency (KWD), default 1.0 for KWD';



-- Create function to validate currency operations

CREATE OR REPLACE FUNCTION public.validate_currency_data()

RETURNS TRIGGER AS $$

BEGIN

  -- If currency is KWD, ensure exchange rate is 1.0 and original_amount equals amount

  IF NEW.currency_code = 'KWD' THEN

    NEW.exchange_rate := 1.0;

    NEW.original_amount := NEW.amount;

  END IF;

  

  -- If currency is not KWD, ensure original_amount and exchange_rate are properly set

  IF NEW.currency_code != 'KWD' THEN

    -- Calculate amount in KWD if not already set correctly

    IF NEW.original_amount IS NOT NULL AND NEW.exchange_rate IS NOT NULL THEN

      NEW.amount := NEW.original_amount * NEW.exchange_rate;

    END IF;

  END IF;

  

  RETURN NEW;

END;

$$ LANGUAGE plpgsql;



-- Create trigger to validate currency data on insert/update

CREATE TRIGGER validate_currency_data_trigger

  BEFORE INSERT OR UPDATE ON public.expenses

  FOR EACH ROW

  EXECUTE FUNCTION public.validate_currency_data();

>>>>>>>
